/*
 * SPDX-FileCopyrightText: 2022-2023 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "esp_adc/adc_oneshot.h"
#include "esp_adc/adc_cali.h"
#include "esp_adc/adc_cali_scheme.h"

const static char *TAG = "ESP32C3_ADC_EXAMPLE";

/*---------------------------------------------------------------
        ADC General Macros for ESP32-C3
---------------------------------------------------------------*/
// ESP32-C3 ADC1 Channels
#define EXAMPLE_ADC1_CHAN0 ADC_CHANNEL_3
#define EXAMPLE_ADC1_CHAN1 ADC_CHANNEL_4

#define EXAMPLE_ADC_ATTEN ADC_ATTEN_DB_12

// NTC温度传感器参数 (根据您的具体NTC型号调整这些参数)
#define VCC_VOLTAGE 3300.0f    // 供电电压 (mV)
#define R_FIXED 10000.0f       // 固定电阻值 (欧姆) - 根据原理图R20/R15
#define NTC_B_VALUE 3950.0f    // NTC B值 (K) - 需要根据您的NTC规格书确认
#define NTC_R25 10000.0f       // 25°C时的NTC电阻值 (欧姆)
#define TEMP_REFERENCE 298.15f // 25°C对应的开尔文温度

// 分压电路配置 - 根据您的实际电路调整
#define NTC_IS_UPPER_RESISTOR 0 // 0: NTC在下方(接地), 1: NTC在上方(接VCC)

static int adc_raw[10];
static int voltage[10];
static float temperature[10];
static bool example_adc_calibration_init(adc_unit_t unit, adc_channel_t channel, adc_atten_t atten, adc_cali_handle_t *out_handle);
static void example_adc_calibration_deinit(adc_cali_handle_t handle);
static float voltage_to_temperature(int voltage_mv);

void app_main(void)
{
    //-------------ADC1 Init---------------//
    adc_oneshot_unit_handle_t adc1_handle;
    adc_oneshot_unit_init_cfg_t init_config1 = {
        .unit_id = ADC_UNIT_1,
    };
    ESP_ERROR_CHECK(adc_oneshot_new_unit(&init_config1, &adc1_handle));

    //-------------ADC1 Config---------------//
    adc_oneshot_chan_cfg_t config = {
        .bitwidth = ADC_BITWIDTH_DEFAULT,
        .atten = EXAMPLE_ADC_ATTEN,
    };
    ESP_ERROR_CHECK(adc_oneshot_config_channel(adc1_handle, EXAMPLE_ADC1_CHAN0, &config));
    ESP_ERROR_CHECK(adc_oneshot_config_channel(adc1_handle, EXAMPLE_ADC1_CHAN1, &config));

    //-------------ADC1 Calibration Init---------------//
    adc_cali_handle_t adc1_cali_chan0_handle = NULL;
    adc_cali_handle_t adc1_cali_chan1_handle = NULL;
    bool do_calibration1_chan0 = example_adc_calibration_init(ADC_UNIT_1, EXAMPLE_ADC1_CHAN0, EXAMPLE_ADC_ATTEN, &adc1_cali_chan0_handle);
    bool do_calibration1_chan1 = example_adc_calibration_init(ADC_UNIT_1, EXAMPLE_ADC1_CHAN1, EXAMPLE_ADC_ATTEN, &adc1_cali_chan1_handle);

    while (1)
    {
        ESP_ERROR_CHECK(adc_oneshot_read(adc1_handle, EXAMPLE_ADC1_CHAN0, &adc_raw[0]));
        ESP_LOGI(TAG, "ADC%d Channel[%d] Raw Data: %d", ADC_UNIT_1 + 1, EXAMPLE_ADC1_CHAN0, adc_raw[0]);
        if (do_calibration1_chan0)
        {
            ESP_ERROR_CHECK(adc_cali_raw_to_voltage(adc1_cali_chan0_handle, adc_raw[0], &voltage[0]));
            temperature[0] = voltage_to_temperature(voltage[0]);
            ESP_LOGI(TAG, "ADC%d Channel[%d] Voltage: %d mV, Temperature: %.2f °C", ADC_UNIT_1 + 1, EXAMPLE_ADC1_CHAN0, voltage[0], temperature[0]);
        }
        vTaskDelay(pdMS_TO_TICKS(1000));

        ESP_ERROR_CHECK(adc_oneshot_read(adc1_handle, EXAMPLE_ADC1_CHAN1, &adc_raw[1]));
        ESP_LOGI(TAG, "ADC%d Channel[%d] Raw Data: %d", ADC_UNIT_1 + 1, EXAMPLE_ADC1_CHAN1, adc_raw[1]);
        if (do_calibration1_chan1)
        {
            ESP_ERROR_CHECK(adc_cali_raw_to_voltage(adc1_cali_chan1_handle, adc_raw[1], &voltage[1]));
            temperature[1] = voltage_to_temperature(voltage[1]);
            ESP_LOGI(TAG, "ADC%d Channel[%d] Voltage: %d mV, Temperature: %.2f °C", ADC_UNIT_1 + 1, EXAMPLE_ADC1_CHAN1, voltage[1], temperature[1]);
        }
        vTaskDelay(pdMS_TO_TICKS(1000));
    }

    // Tear Down
    ESP_ERROR_CHECK(adc_oneshot_del_unit(adc1_handle));
    if (do_calibration1_chan0)
    {
        example_adc_calibration_deinit(adc1_cali_chan0_handle);
    }
    if (do_calibration1_chan1)
    {
        example_adc_calibration_deinit(adc1_cali_chan1_handle);
    }
}

/*---------------------------------------------------------------
        ADC Calibration
---------------------------------------------------------------*/
static bool example_adc_calibration_init(adc_unit_t unit, adc_channel_t channel, adc_atten_t atten, adc_cali_handle_t *out_handle)
{
    adc_cali_handle_t handle = NULL;
    esp_err_t ret = ESP_FAIL;
    bool calibrated = false;

#if ADC_CALI_SCHEME_CURVE_FITTING_SUPPORTED
    // ESP32-C3 supports Curve Fitting calibration scheme
    ESP_LOGI(TAG, "calibration scheme version is %s", "Curve Fitting");
    adc_cali_curve_fitting_config_t cali_config = {
        .unit_id = unit,
        .chan = channel,
        .atten = atten,
        .bitwidth = ADC_BITWIDTH_DEFAULT,
    };
    ret = adc_cali_create_scheme_curve_fitting(&cali_config, &handle);
    if (ret == ESP_OK)
    {
        calibrated = true;
    }
#endif

    *out_handle = handle;
    if (ret == ESP_OK)
    {
        ESP_LOGI(TAG, "Calibration Success");
    }
    else if (ret == ESP_ERR_NOT_SUPPORTED || !calibrated)
    {
        ESP_LOGW(TAG, "eFuse not burnt, skip software calibration");
    }
    else
    {
        ESP_LOGE(TAG, "Invalid arg or no memory");
    }

    return calibrated;
}

static void example_adc_calibration_deinit(adc_cali_handle_t handle)
{
#if ADC_CALI_SCHEME_CURVE_FITTING_SUPPORTED
    ESP_LOGI(TAG, "deregister %s calibration scheme", "Curve Fitting");
    ESP_ERROR_CHECK(adc_cali_delete_scheme_curve_fitting(handle));
#endif
}

/*---------------------------------------------------------------
        Temperature Conversion Function
---------------------------------------------------------------*/
static float voltage_to_temperature(int voltage_mv)
{
    float v_adc = (float)voltage_mv;
    float r_ntc;

    // 防止除零错误和异常值
    if (v_adc >= VCC_VOLTAGE)
    {
        ESP_LOGW(TAG, "ADC voltage >= VCC, using VCC-1mV for calculation");
        v_adc = VCC_VOLTAGE - 1.0f;
    }
    if (v_adc <= 0.0f)
    {
        ESP_LOGW(TAG, "ADC voltage <= 0, using 1mV for calculation");
        v_adc = 1.0f;
    }

    // 根据分压电路配置计算NTC电阻值
    if (NTC_IS_UPPER_RESISTOR)
    {
        // NTC在上方: V_adc = VCC * R_fixed / (R_ntc + R_fixed)
        // 解得: R_ntc = R_fixed * (VCC - V_adc) / V_adc
        r_ntc = R_FIXED * (VCC_VOLTAGE - v_adc) / v_adc;
    }
    else
    {
        // NTC在下方: V_adc = VCC * R_ntc / (R_fixed + R_ntc)
        // 解得: R_ntc = R_fixed * V_adc / (VCC - V_adc)
        r_ntc = R_FIXED * v_adc / (VCC_VOLTAGE - v_adc);
    }

    // 检查计算出的电阻值是否合理
    if (r_ntc <= 0.0f || r_ntc > 1000000.0f)
    {
        ESP_LOGW(TAG, "Calculated NTC resistance out of range: %.1f Ω", r_ntc);
        return -999.0f; // 返回错误值
    }

    // 使用Steinhart-Hart方程的简化版本 (B参数方程)
    // 1/T = 1/T0 + (1/B) * ln(R/R0)
    float temp_kelvin = 1.0f / (1.0f / TEMP_REFERENCE + (1.0f / NTC_B_VALUE) * logf(r_ntc / NTC_R25));
    float temp_celsius = temp_kelvin - 273.15f;

    // 详细调试信息
    ESP_LOGI(TAG, "Debug: V_adc=%.1fmV, R_ntc=%.1fΩ, Temp=%.2f°C", v_adc, r_ntc, temp_celsius);

    return temp_celsius;
}
